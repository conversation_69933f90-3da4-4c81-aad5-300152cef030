# v4.0架构权限机制实施指南

## 权限机制总览

### 权限层级体系
```
Step5: 全局重构权限（最高权限）
  ↓
Step4: 结构化内容重构权限（高级权限）
  ↓
Step3: 跨类型修改权限（核心权限）
  ↓
Step2: 推荐验证权限（基础权限）
  ↓
Step1: 内容分析权限（分析权限）
```

### 权限覆盖范围

| 步骤 | 可修改的控件来源 | 修改权限范围 | 典型修改场景 |
|------|------------------|--------------|--------------|
| Step2 | Step1推荐的TITLE/TEXT | 推荐验证、样式调整 | 样式优化、层级调整 |
| Step3 | Step1推荐的LIST + Step2生成的控件 | 跨类型修改、结构调整 | TEXT→LIST升级 |
| Step4 | Step1推荐的TABLE + 前序生成的控件 | 结构化重构、数据优化 | LIST→TABLE升级 |
| Step5 | 所有前序生成的控件 | 全局重构、最终优化 | 全局结构调整 |

## 具体权限实施

### Step2: 推荐验证权限

#### 权限范围
- ✅ 验证Step1推荐给自己的TITLE和TEXT控件
- ✅ 调整控件样式（DOCUMENT/SECTION/PARAGRAPH/ENTRY）
- ✅ 调整TEXT样式（FLOAT/BOARD/EMPHASIS/PLAIN）
- ❌ 不能修改其他类型的推荐

#### 实施要点
```json
// 权限行使示例
{
  "original_recommendation": {
    "type": "TEXT",
    "style": "PLAIN"
  },
  "final_decision": {
    "type": "TEXT",
    "style": "BOARD"
  },
  "adjustment_reason": "发现分析性关键词'趋势分析'"
}
```

### Step3: 跨类型修改权限

#### 权限范围
- ✅ 处理Step1推荐的LIST控件
- ✅ 将Step2生成的TEXT控件升级为LIST控件
- ✅ 调整Step2生成的TITLE控件（避免标题重复）
- ❌ 不能修改TABLE/CHART相关内容

#### 关键修改场景
**TEXT → LIST升级**：
```json
// 修改前（Step2生成）
{
  "type": "TEXT",
  "content": "1. 核心优势：地段优越 2. 交通便利：地铁直达"
}

// 修改后（Step3升级）
{
  "type": "LIST",
  "style": "SERIAL",
  "content": [
    {"title": "核心优势", "content": "地段优越"},
    {"title": "交通便利", "content": "地铁直达"}
  ]
}
```

#### 权限行使条件
- **必要条件**：内容确实具有列表结构特征
- **充分条件**：列表展示效果明显优于原有展示
- **记录要求**：详细记录修改原因和分析依据

### Step4: 结构化内容重构权限

#### 权限范围
- ✅ 处理Step1推荐的TABLE控件
- ✅ 将Step3生成的LIST控件升级为TABLE控件
- ✅ 将Step2生成的TEXT控件升级为TABLE控件
- ✅ 重新分析前序控件的数据结构特征
- ❌ 不能修改CHART相关决策

#### 关键修改场景
**LIST → TABLE升级**：
```json
// 修改前（Step3生成）
{
  "type": "LIST",
  "content": [
    {"title": "2室", "content": "35%"},
    {"title": "3室", "content": "45%"}
  ]
}

// 修改后（Step4升级）
{
  "type": "TABLE",
  "cols": ["户型", "占比"],
  "content": [
    [{"type": "TEXT", "content": "2室"}, {"type": "TEXT", "content": "35%"}],
    [{"type": "TEXT", "content": "3室"}, {"type": "TEXT", "content": "45%"}]
  ]
}
```

#### 权限行使条件
- **数据结构条件**：数据具有明确的行列结构特征
- **展示效果条件**：表格展示明显优于列表展示
- **对比价值条件**：数据适合进行横向和纵向对比

### Step5: 全局重构权限

#### 权限范围
- ✅ 处理所有图表转换决策
- ✅ 修改任何前序步骤生成的控件
- ✅ 调整整个文档的结构和顺序
- ✅ 进行全局性的质量优化
- ✅ 拥有最终决策权

#### 全局重构场景
**文档结构优化**：
```json
// 重新调整序列编号
// 优化控件顺序
// 解决全局标题重复问题
// 确保层级关系合理
```

**跨步骤控件修改**：
```json
// 发现Step2的TEXT控件实际应为CHART
// 发现Step3的LIST控件更适合TABLE展示
// 基于全局视角进行最终优化
```

#### 权限行使原则
- **全局视角原则**：基于完整文档进行决策
- **用户体验原则**：优先考虑最终用户的使用体验
- **质量优先原则**：确保最终输出的质量最优

## 权限冲突处理

### 冲突识别机制
```json
{
  "conflict_type": "cross_step_modification",
  "conflict_description": "Step4要修改Step3已确定的LIST控件",
  "resolution_strategy": "基于数据结构特征进行判断",
  "final_decision": "升级为TABLE控件",
  "decision_basis": [
    "数据具有明确的行列结构",
    "表格展示效果更优",
    "用户理解更容易"
  ]
}
```

### 冲突解决优先级
1. **数据忠实性**：绝不违背原始内容
2. **展示效果**：选择最佳的展示方式
3. **用户体验**：优先考虑用户理解
4. **结构合理性**：确保文档结构清晰

### 安全边界机制
**允许的修改**：
- ✅ 基于充分分析依据的控件类型转换
- ✅ 基于内容特征的样式调整
- ✅ 基于展示效果的结构优化

**禁止的修改**：
- ❌ 修改或添加原始内容
- ❌ 无依据的随意修改
- ❌ 违背数据忠实性的修改

## 权限使用最佳实践

### 1. 权限行使前的检查清单
- [ ] 是否有充分的分析依据？
- [ ] 修改是否确实改善展示效果？
- [ ] 是否违背了数据忠实性原则？
- [ ] 是否记录了详细的修改原因？

### 2. 修改记录标准格式
```json
{
  "modification_id": "mod_001",
  "step": 3,
  "modification_type": "cross_type_change",
  "original_widget": {
    "serial": "1.1",
    "type": "TEXT",
    "step_created": 2
  },
  "modified_widget": {
    "serial": "1.1",
    "type": "LIST",
    "step_modified": 3
  },
  "analysis_evidence": [
    "包含明确的数字序号分隔",
    "每项都有标题和内容结构",
    "列表展示效果明显更优"
  ],
  "quality_impact": "positive",
  "user_experience_impact": "improved"
}
```

### 3. 质量保证机制
- **修改前验证**：确保修改的必要性和合理性
- **修改后检查**：验证修改是否达到预期效果
- **全局一致性**：确保修改不影响整体结构
- **用户反馈**：收集实际使用效果的反馈

## 实施监控与优化

### 1. 权限使用统计
- **修改频率监控**：各步骤的修改频率统计
- **修改成功率**：修改后质量改善的比例
- **用户满意度**：最终输出质量的用户反馈

### 2. 持续优化策略
- **权限边界调整**：基于使用效果调整权限范围
- **决策规则优化**：优化各步骤的决策逻辑
- **冲突处理完善**：完善冲突识别和解决机制

### 3. 风险控制机制
- **过度修改预警**：监控修改频率，避免过度修改
- **质量回退机制**：发现修改不当时的回滚机制
- **安全边界强化**：持续强化数据忠实性边界

## 总结

这套权限机制确保了v4.0架构的"渐进式优化"理念得到充分实现，通过明确的权限层级、详细的实施指南和完善的冲突处理机制，让后续步骤能够基于更充分的信息对前序决策进行合理优化，同时保证系统的稳定性和输出质量。
