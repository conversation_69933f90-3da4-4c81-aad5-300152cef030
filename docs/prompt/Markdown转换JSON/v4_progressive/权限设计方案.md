# v4.0架构权限设计方案

## 核心权限理念

### 渐进式优化权限原则
- **后续优先原则**：后续步骤拥有更充分的信息，有权覆盖前序决策
- **全局优化原则**：每个步骤都可以从全局角度重新评估和调整
- **信息增量原则**：随着处理深入，信息越来越充分，决策权限越来越大

## 权限层级设计

### 1. 推荐验证权限（基础权限）
**权限范围**：重新评估推荐给自己处理的控件类型
**适用步骤**：Step2-5
**典型场景**：
- Step3验证LIST推荐的合理性
- Step4验证TABLE推荐的合理性

### 2. 跨类型修改权限（核心权限）
**权限范围**：修改前序步骤已确定为其他类型的控件
**适用步骤**：Step3-5
**典型场景**：
- Step3发现Step2生成的TEXT控件实际应为LIST控件
- Step4发现Step3生成的LIST控件实际应为TABLE控件
- Step5发现Step4生成的TABLE控件更适合CHART展示

### 3. 结构化内容重构权限（高级权限）
**权限范围**：对前序步骤已生成的具体控件进行重新判断和修改
**适用步骤**：Step4-5
**典型场景**：
- Step4重新分析Step2生成的TEXT控件，发现包含表格数据
- Step5重新分析Step3生成的LIST控件，发现更适合图表展示

### 4. 全局重构权限（最高权限）
**权限范围**：基于全局分析对整个文档结构进行重新组织
**适用步骤**：Step5
**典型场景**：
- 发现文档结构不合理，重新调整控件顺序
- 发现标题层级混乱，重新分配序列编号

## 具体权限场景

### Step3权限场景

#### 场景1：TEXT → LIST升级
```json
// Step2生成的TEXT控件
{
  "serial": "1.1",
  "type": "TEXT",
  "style": "PLAIN",
  "content": "1. 核心优势：地段优越 2. 交通便利：地铁直达 3. 配套完善：商业齐全"
}

// Step3重新判断后
{
  "serial": "1.1",
  "type": "LIST",
  "style": "SERIAL",
  "title": "",
  "content": [
    {"title": "核心优势", "content": "地段优越"},
    {"title": "交通便利", "content": "地铁直达"},
    {"title": "配套完善", "content": "商业齐全"}
  ]
}
```

#### 场景2：TITLE结构调整
```json
// Step2生成的结构
{
  "serial": "1",
  "type": "TITLE",
  "style": "SECTION",
  "title": "房源优势"
}

// Step3发现后续有LIST控件，调整为避免重复
{
  "serial": "1",
  "type": "TITLE", 
  "style": "SECTION",
  "title": "房源优势"
}
// 对应的LIST控件title设为空，避免重复
```

### Step4权限场景

#### 场景1：LIST → TABLE升级
```json
// Step3生成的LIST控件
{
  "serial": "2.1",
  "type": "LIST",
  "style": "ITEM",
  "content": [
    {"title": "2室", "content": "35%"},
    {"title": "3室", "content": "45%"},
    {"title": "4室", "content": "20%"}
  ]
}

// Step4重新判断为表格数据
{
  "serial": "2.1",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "户型分布",
  "cols": ["户型", "占比"],
  "content": [
    [
      {"type": "TEXT", "content": "2室"},
      {"type": "TEXT", "content": "35%"}
    ],
    [
      {"type": "TEXT", "content": "3室"},
      {"type": "TEXT", "content": "45%"}
    ],
    [
      {"type": "TEXT", "content": "4室"},
      {"type": "TEXT", "content": "20%"}
    ]
  ]
}
```

### Step5权限场景

#### 场景1：TABLE → CHART转换
```json
// Step4生成的TABLE控件
{
  "serial": "3.1",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "价格趋势",
  "cols": ["月份", "均价"],
  "content": [...]
}

// Step5转换为图表
{
  "serial": "3.1",
  "type": "CHART",
  "style": "LINE",
  "title": "价格趋势（万元/㎡）",
  "cols": ["11月", "12月", "1月"],
  "content": [
    {
      "title": "均价走势",
      "content": [10.6, 10.5, 10.9]
    }
  ]
}
```

#### 场景2：全局结构优化
```json
// 发现序列编号混乱，重新分配
// 发现控件顺序不合理，重新排序
// 发现标题重复问题，统一处理
```

## 权限冲突处理机制

### 1. 权限优先级规则
- **信息充分性优先**：拥有更充分信息的步骤优先
- **专业性优先**：专门处理某类型控件的步骤优先
- **全局性优先**：后续步骤的全局视角优先

### 2. 修改安全边界
**允许的修改**：
- 控件类型转换（基于充分的分析依据）
- 控件样式调整（基于内容特征重新分析）
- 控件结构优化（基于更好的展示效果）

**禁止的修改**：
- 修改原始内容（绝对禁止）
- 添加虚构信息（绝对禁止）
- 无依据的随意修改（必须有充分理由）

### 3. 冲突解决策略
**发生冲突时的处理顺序**：
1. **数据忠实性检查**：确保修改不违背原始内容
2. **展示效果评估**：评估修改后的展示效果
3. **用户体验考虑**：选择最有利于用户理解的方案
4. **记录决策过程**：完整记录冲突解决的原因和过程

## 权限执行机制

### 1. 权限检查流程
```
1. 识别需要修改的控件
2. 评估修改的必要性和合理性
3. 检查是否有充分的分析依据
4. 执行修改并记录原因
5. 验证修改后的效果
```

### 2. 修改记录格式
```json
{
  "modification_type": "cross_type_change",
  "original_widget": {
    "serial": "1.1",
    "type": "TEXT",
    "step_created": 2
  },
  "modified_widget": {
    "serial": "1.1", 
    "type": "LIST",
    "step_modified": 3
  },
  "modification_reason": "发现内容具有明确的列表结构特征",
  "analysis_evidence": [
    "包含数字序号分隔",
    "每项都有明确的标题和内容",
    "更适合列表展示"
  ]
}
```

### 3. 权限边界监控
- **修改频率监控**：避免过度修改导致的不稳定
- **修改质量评估**：确保修改确实提升了展示效果
- **用户反馈收集**：基于实际使用效果调整权限策略

## 实施建议

### 1. 渐进式权限开放
- **第一阶段**：开放推荐验证权限，验证基础机制
- **第二阶段**：开放跨类型修改权限，处理明显的类型错误
- **第三阶段**：开放结构化内容重构权限，优化复杂情况
- **第四阶段**：开放全局重构权限，实现全面优化

### 2. 权限使用原则
- **谨慎使用**：只在有充分依据时才行使权限
- **记录完整**：每次权限使用都要有完整记录
- **效果验证**：修改后要验证是否确实改善了效果
- **用户导向**：始终以提升用户体验为目标

### 3. 质量保证机制
- **多层验证**：修改前后都要进行质量检查
- **回滚机制**：发现修改不当时能够回滚
- **学习优化**：基于修改效果持续优化权限策略

## 总结

这套权限设计方案确保了v4.0架构的"渐进式优化"理念得到充分实现，让后续步骤能够基于更充分的信息对前序决策进行优化和修正，同时通过完善的安全边界和冲突处理机制，确保修改的合理性和系统的稳定性。
